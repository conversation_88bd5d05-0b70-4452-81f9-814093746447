#!/usr/bin/env python3
"""
Simple debugging helper for when VSCode Python extension isn't available
"""

import sys
import traceback
import pdb
from rich.console import Console
from rich.traceback import install

# Install rich traceback handler for better error display
install(show_locals=True)

console = Console()

def debug_print(*args, **kwargs):
    """Enhanced print function for debugging"""
    console.print("[bold blue][DEBUG][/bold blue]", *args, **kwargs)

def debug_vars(**kwargs):
    """Print variables with their names and values"""
    console.print("[bold yellow][VARIABLES][/bold yellow]")
    for name, value in kwargs.items():
        console.print(f"  {name} = {repr(value)}")

def debug_function(func):
    """Decorator to add debugging to functions"""
    def wrapper(*args, **kwargs):
        console.print(f"[bold green][CALL][/bold green] {func.__name__}({args}, {kwargs})")
        try:
            result = func(*args, **kwargs)
            console.print(f"[bold green][RETURN][/bold green] {func.__name__} -> {repr(result)}")
            return result
        except Exception as e:
            console.print(f"[bold red][ERROR][/bold red] {func.__name__} raised {type(e).__name__}: {e}")
            raise
    return wrapper

def start_debugger():
    """Start the Python debugger"""
    console.print("[bold yellow]Starting Python debugger (pdb)[/bold yellow]")
    console.print("Common pdb commands:")
    console.print("  n (next line)")
    console.print("  s (step into)")
    console.print("  c (continue)")
    console.print("  l (list code)")
    console.print("  p <var> (print variable)")
    console.print("  q (quit)")
    pdb.set_trace()

if __name__ == "__main__":
    console.print("[bold green]Debug Helper Loaded[/bold green]")
    console.print("Available functions:")
    console.print("  debug_print() - Enhanced print for debugging")
    console.print("  debug_vars() - Print variables with names")
    console.print("  @debug_function - Decorator for function debugging")
    console.print("  start_debugger() - Start pdb debugger")
