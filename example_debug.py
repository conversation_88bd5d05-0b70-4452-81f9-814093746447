#!/usr/bin/env python3
"""
Example script showing how to use debugging without VSCode extension
"""

from debug_helper import debug_print, debug_vars, debug_function, start_debugger

@debug_function
def calculate_sum(a, b):
    """Example function with debugging"""
    result = a + b
    return result

def main():
    debug_print("Starting example script")
    
    x = 10
    y = 20
    debug_vars(x=x, y=y)
    
    result = calculate_sum(x, y)
    debug_print(f"Final result: {result}")
    
    # Uncomment the next line to start interactive debugging
    # start_debugger()
    
    debug_print("<PERSON><PERSON><PERSON> completed successfully")

if __name__ == "__main__":
    main()
