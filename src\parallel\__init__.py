"""Parallel processing module for cryptocurrency backtesting system.

This module provides high-performance parallel processing capabilities optimized
for multi-core systems, enabling efficient strategy optimization and backtesting
across multiple CPU cores.

Classes:
    ParallelExecutor: Main parallel execution coordinator
    WorkerPool: Managed worker pool for parallel tasks
    TaskQueue: Thread-safe task queue management
    ResultCollector: Collect and aggregate results from workers
    MemoryManager: Monitor and manage memory usage across workers
    ProcessMonitor: Monitor worker processes and system resources

Functions:
    parallel_backtest: Run multiple backtests in parallel
    parallel_optimize: Parallel parameter optimization
    create_worker_pool: Factory function for worker pool creation
    monitor_resources: Monitor system resource usage

Example:
    >>> from src.parallel import ParallelExecutor, WorkerPool
    >>> from src.strategies import MovingAverageCrossover
    >>> 
    >>> # Create worker pool optimized for Ryzen 9 7950X
    >>> pool = WorkerPool(max_workers=16, memory_limit_gb=6)
    >>> executor = ParallelExecutor(pool)
    >>> 
    >>> # Define parameter combinations
    >>> param_combinations = [
    >>>     {'fast_period': 10, 'slow_period': 30},
    >>>     {'fast_period': 15, 'slow_period': 45},
    >>>     {'fast_period': 20, 'slow_period': 60}
    >>> ]
    >>> 
    >>> # Run parallel backtests
    >>> results = executor.parallel_backtest(
    >>>     strategy_class=MovingAverageCrossover,
    >>>     data=market_data,
    >>>     param_combinations=param_combinations
    >>> )
    >>> 
    >>> # Get best performing parameters
    >>> best_result = max(results, key=lambda r: r.sharpe_ratio)
    >>> print(f"Best Sharpe: {best_result.sharpe_ratio:.4f}")
"""

# Import parallel processing components
try:
    from .executor import ParallelExecutor  # type: ignore
    # Other imports will be added as modules are created
except ImportError:
    pass

# Module metadata
__version__ = "0.1.0"
__author__ = "Crypto Backtesting Team"

# Hardware-optimized defaults for Ryzen 9 7950X
HARDWARE_OPTIMIZED_CONFIG = {
    'max_workers': 16,          # 16 cores for optimal performance
    'memory_per_worker': 6,     # 6GB per worker (96GB / 16)
    'chunk_size': 100,          # Optimal chunk size for parameter sets
    'queue_size': 1000,         # Task queue size
    'timeout': 3600,            # 1 hour timeout per task
    'cpu_affinity': True,       # Enable CPU affinity for better cache locality
    'numa_aware': True,         # NUMA-aware scheduling
    'hyperthreading': True      # Utilize hyperthreading (32 logical cores)
}

# Conservative defaults for general use
CONSERVATIVE_CONFIG = {
    'max_workers': 8,           # Conservative worker count
    'memory_per_worker': 4,     # 4GB per worker
    'chunk_size': 50,           # Smaller chunk size
    'queue_size': 500,          # Smaller queue
    'timeout': 1800,            # 30 minutes timeout
    'cpu_affinity': False,      # Disable CPU affinity
    'numa_aware': False,        # Disable NUMA awareness
    'hyperthreading': False     # Conservative threading
}

# Memory management thresholds
MEMORY_THRESHOLDS = {
    'warning': 0.8,             # 80% memory usage warning
    'critical': 0.9,            # 90% memory usage critical
    'cleanup': 0.85,            # 85% trigger cleanup
    'gc_interval': 100,         # Garbage collection interval
    'worker_restart': 0.95      # 95% restart worker
}

# Performance monitoring settings
MONITORING_CONFIG = {
    'update_interval': 5,       # 5 seconds monitoring interval
    'log_performance': True,    # Log performance metrics
    'track_memory': True,       # Track memory usage
    'track_cpu': True,          # Track CPU usage
    'track_io': True,           # Track I/O operations
    'alert_thresholds': True    # Enable threshold alerts
}

# Task scheduling strategies
SCHEDULING_STRATEGIES = {
    'round_robin': 'Round-robin task distribution',
    'load_balanced': 'Load-balanced distribution',
    'priority_based': 'Priority-based scheduling',
    'memory_aware': 'Memory-aware scheduling',
    'cpu_aware': 'CPU-aware scheduling'
}

# Error handling and retry settings
ERROR_HANDLING_CONFIG = {
    'max_retries': 3,           # Maximum retry attempts
    'retry_delay': 1,           # Delay between retries (seconds)
    'exponential_backoff': True, # Use exponential backoff
    'fail_fast': False,         # Continue on individual failures
    'log_errors': True,         # Log all errors
    'error_threshold': 0.1      # 10% error threshold before stopping
}

def parallel_backtest(strategy_class, data, param_combinations, config=None, **kwargs):
    """Run multiple backtests in parallel.
    
    Args:
        strategy_class: Strategy class to backtest
        data: Market data for backtesting
        param_combinations: List of parameter dictionaries
        config: Parallel execution configuration
        **kwargs: Additional backtest parameters
        
    Returns:
        List[BacktestResult]: Results from all parallel backtests
    """
    # This would be implemented when the actual parallel classes are created
    raise NotImplementedError("parallel_backtest will be implemented with parallel classes")

def parallel_optimize(strategy_class, data, param_space, config=None, **kwargs):
    """Run parallel parameter optimization.
    
    Args:
        strategy_class: Strategy class to optimize
        data: Market data for backtesting
        param_space: Parameter space definition
        config: Parallel execution configuration
        **kwargs: Additional optimization parameters
        
    Returns:
        OptimizationResult: Optimization results
    """
    # This would be implemented when the actual parallel classes are created
    raise NotImplementedError("parallel_optimize will be implemented with parallel classes")

# Export public API
__all__ = [
    "ParallelExecutor",
    "WorkerPool",
    "TaskQueue",
    "ResultCollector",
    "MemoryManager",
    "ProcessMonitor",
    "TaskScheduler",
    "parallel_backtest",
    "parallel_optimize",
    "HARDWARE_OPTIMIZED_CONFIG",
    "CONSERVATIVE_CONFIG",
    "MEMORY_THRESHOLDS",
    "MONITORING_CONFIG",
    "SCHEDULING_STRATEGIES",
    "ERROR_HANDLING_CONFIG",
]