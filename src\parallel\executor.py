"""Parallel execution module for cryptocurrency backtesting system.

This module provides various parallel execution strategies for backtesting operations,
including multiprocessing, threading, and distributed computing capabilities.
"""

import os
import time
import threading
import multiprocessing as mp
from multiprocessing import Pool, Process, Queue, Manager, Value, Array
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, ProcessPoolExecutor, as_completed
from typing import Dict, List, Optional, Any, Callable, Union, Tuple, Iterator
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import queue
import pickle
import traceback
import signal
import psutil
from pathlib import Path
import json

# Third-party parallel libraries
try:
    import ray  # type: ignore[import-not-found]
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False
    ray = None  # type: ignore[assignment]

try:
    from dask.distributed import Client, as_completed as dask_as_completed
    from dask import delayed
    DASK_AVAILABLE = True
except ImportError:
    DASK_AVAILABLE = False
    Client = None  # type: ignore[misc,assignment]
    delayed = None  # type: ignore[misc,assignment]

try:
    from joblib import Parallel, delayed as joblib_delayed  # type: ignore[import-untyped]
    JOBLIB_AVAILABLE = True
except ImportError:
    JOBLIB_AVAILABLE = False
    Parallel = None
    joblib_delayed = None

import numpy as np
import pandas as pd  # type: ignore[import-untyped]

from ..utils.logging import get_logger, log_performance
from ..utils.config import get_config
from ..monitoring.metrics import get_metrics_manager, time_operation


class ExecutionMode(Enum):
    """Parallel execution modes."""
    SEQUENTIAL = "sequential"
    THREADING = "threading"
    MULTIPROCESSING = "multiprocessing"
    RAY = "ray"
    DASK = "dask"
    JOBLIB = "joblib"


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class TaskResult:
    """Result of a parallel task."""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    worker_id: Optional[str] = None
    memory_usage: Optional[float] = None
    
    @property
    def duration(self) -> Optional[float]:
        """Task duration in seconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    @property
    def success(self) -> bool:
        """Whether task completed successfully."""
        return self.status == TaskStatus.COMPLETED and self.error is None


@dataclass
class ExecutionConfig:
    """Configuration for parallel execution."""
    mode: ExecutionMode = ExecutionMode.MULTIPROCESSING
    max_workers: Optional[int] = None
    chunk_size: Optional[int] = None
    timeout: Optional[float] = None
    memory_limit: Optional[str] = None  # e.g., "4GB"
    
    # Ray-specific settings
    ray_address: Optional[str] = None
    ray_runtime_env: Optional[Dict] = None
    
    # Dask-specific settings
    dask_scheduler_address: Optional[str] = None
    dask_client_kwargs: Optional[Dict] = None
    
    # Resource management
    cpu_limit: Optional[float] = None
    memory_per_worker: Optional[str] = None
    
    # Error handling
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # Monitoring
    enable_monitoring: bool = True
    progress_callback: Optional[Callable] = None


@dataclass
class ExecutionStats:
    """Statistics for parallel execution."""
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    cancelled_tasks: int
    
    total_duration: float
    avg_task_duration: float
    min_task_duration: float
    max_task_duration: float
    
    total_memory_usage: float
    peak_memory_usage: float
    avg_memory_per_task: float
    
    worker_count: int
    cpu_utilization: float
    
    start_time: datetime
    end_time: datetime
    
    @property
    def success_rate(self) -> float:
        """Task success rate."""
        if self.total_tasks == 0:
            return 0.0
        return self.completed_tasks / self.total_tasks
    
    @property
    def throughput(self) -> float:
        """Tasks per second."""
        if self.total_duration == 0:
            return 0.0
        return self.completed_tasks / self.total_duration


class TaskMonitor:
    """Monitors task execution progress and resource usage."""
    
    def __init__(self, enable_monitoring: bool = True):
        """Initialize task monitor.
        
        Args:
            enable_monitoring: Whether to enable monitoring
        """
        self.enable_monitoring = enable_monitoring
        self.logger = get_logger("task_monitor")
        
        # Task tracking
        self.tasks: Dict[str, TaskResult] = {}
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        
        # Resource monitoring
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_monitoring = threading.Event()
        self._resource_data: List[Dict] = []
        
        # Metrics
        self.metrics_manager = get_metrics_manager() if enable_monitoring else None
    
    def start_monitoring(self) -> None:
        """Start resource monitoring."""
        if not self.enable_monitoring:
            return
        
        self.start_time = datetime.now()
        self._stop_monitoring.clear()
        
        # Start resource monitoring thread
        self._monitor_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self._monitor_thread.start()
        
        self.logger.info("Task monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop resource monitoring."""
        if not self.enable_monitoring:
            return
        
        self.end_time = datetime.now()
        self._stop_monitoring.set()
        
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        
        self.logger.info("Task monitoring stopped")
    
    def _monitor_resources(self) -> None:
        """Monitor system resources."""
        while not self._stop_monitoring.wait(1.0):  # Check every second
            try:
                # Get system metrics
                cpu_percent = psutil.cpu_percent()
                memory = psutil.virtual_memory()
                
                resource_data = {
                    'timestamp': datetime.now(),
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_used_gb': memory.used / (1024**3),
                    'active_tasks': len([t for t in self.tasks.values() if t.status == TaskStatus.RUNNING])
                }
                
                self._resource_data.append(resource_data)
                
                # Update metrics
                if self.metrics_manager:
                    self.metrics_manager.set_gauge('parallel.cpu_percent', cpu_percent)
                    self.metrics_manager.set_gauge('parallel.memory_percent', memory.percent)
                    self.metrics_manager.set_gauge('parallel.active_tasks', resource_data['active_tasks'])
                
            except Exception as e:
                self.logger.error(f"Resource monitoring error: {e}")
    
    def register_task(self, task_id: str) -> None:
        """Register a new task.
        
        Args:
            task_id: Unique task identifier
        """
        self.tasks[task_id] = TaskResult(
            task_id=task_id,
            status=TaskStatus.PENDING
        )
        
        if self.metrics_manager:
            self.metrics_manager.increment_counter('parallel.tasks_registered')
    
    def start_task(self, task_id: str, worker_id: Optional[str] = None) -> None:
        """Mark task as started.
        
        Args:
            task_id: Task identifier
            worker_id: Worker identifier
        """
        if task_id in self.tasks:
            self.tasks[task_id].status = TaskStatus.RUNNING
            self.tasks[task_id].start_time = datetime.now()
            self.tasks[task_id].worker_id = worker_id
            
            if self.metrics_manager:
                self.metrics_manager.increment_counter('parallel.tasks_started')
    
    def complete_task(
        self,
        task_id: str,
        result: Any = None,
        error: Optional[str] = None,
        memory_usage: Optional[float] = None
    ) -> None:
        """Mark task as completed.
        
        Args:
            task_id: Task identifier
            result: Task result
            error: Error message if failed
            memory_usage: Memory usage in MB
        """
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.end_time = datetime.now()
            task.result = result
            task.error = error
            task.memory_usage = memory_usage
            
            if error:
                task.status = TaskStatus.FAILED
                if self.metrics_manager:
                    self.metrics_manager.increment_counter('parallel.tasks_failed')
            else:
                task.status = TaskStatus.COMPLETED
                if self.metrics_manager:
                    self.metrics_manager.increment_counter('parallel.tasks_completed')
    
    def cancel_task(self, task_id: str) -> None:
        """Mark task as cancelled.
        
        Args:
            task_id: Task identifier
        """
        if task_id in self.tasks:
            self.tasks[task_id].status = TaskStatus.CANCELLED
            self.tasks[task_id].end_time = datetime.now()
            
            if self.metrics_manager:
                self.metrics_manager.increment_counter('parallel.tasks_cancelled')
    
    def get_stats(self) -> ExecutionStats:
        """Get execution statistics.
        
        Returns:
            Execution statistics
        """
        completed_tasks = [t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED]
        failed_tasks = [t for t in self.tasks.values() if t.status == TaskStatus.FAILED]
        cancelled_tasks = [t for t in self.tasks.values() if t.status == TaskStatus.CANCELLED]
        
        # Calculate durations
        durations = [t.duration for t in completed_tasks if t.duration is not None]
        
        # Calculate memory usage
        memory_usages = [t.memory_usage for t in completed_tasks if t.memory_usage is not None]
        
        # Calculate total duration
        total_duration = 0.0
        if self.start_time and self.end_time:
            total_duration = (self.end_time - self.start_time).total_seconds()
        
        # Get resource stats
        cpu_utilization = 0.0
        peak_memory = 0.0
        
        if self._resource_data:
            cpu_utilization = float(np.mean([r['cpu_percent'] for r in self._resource_data]))
            peak_memory = max([r['memory_used_gb'] for r in self._resource_data])
        
        return ExecutionStats(
            total_tasks=len(self.tasks),
            completed_tasks=len(completed_tasks),
            failed_tasks=len(failed_tasks),
            cancelled_tasks=len(cancelled_tasks),
            total_duration=total_duration,
            avg_task_duration=float(np.mean(durations)) if durations else 0.0,
            min_task_duration=min(durations) if durations else 0.0,
            max_task_duration=max(durations) if durations else 0.0,
            total_memory_usage=sum(memory_usages) if memory_usages else 0.0,
            peak_memory_usage=peak_memory,
            avg_memory_per_task=float(np.mean(memory_usages)) if memory_usages else 0.0,
            worker_count=len(set(t.worker_id for t in self.tasks.values() if t.worker_id)),
            cpu_utilization=cpu_utilization,
            start_time=self.start_time or datetime.now(),
            end_time=self.end_time or datetime.now()
        )


class BaseExecutor:
    """Base class for parallel executors."""
    
    def __init__(self, config: ExecutionConfig):
        """Initialize executor.
        
        Args:
            config: Execution configuration
        """
        self.config = config
        self.logger = get_logger(f"{self.__class__.__name__.lower()}")
        self.monitor = TaskMonitor(config.enable_monitoring)
        
        # State
        self._is_initialized = False
        self._shutdown = False
    
    def initialize(self) -> None:
        """Initialize executor resources."""
        if self._is_initialized:
            return
        
        self._initialize_impl()
        self._is_initialized = True
        self.logger.info(f"Executor initialized with mode: {self.config.mode.value}")
    
    def _initialize_impl(self) -> None:
        """Implementation-specific initialization."""
        pass
    
    def shutdown(self) -> None:
        """Shutdown executor and cleanup resources."""
        if self._shutdown:
            return
        
        self._shutdown_impl()
        self._shutdown = True
        self.logger.info("Executor shutdown")
    
    def _shutdown_impl(self) -> None:
        """Implementation-specific shutdown."""
        pass
    
    def execute(
        self,
        func: Callable,
        tasks: List[Any],
        **kwargs
    ) -> List[TaskResult]:
        """Execute tasks in parallel.
        
        Args:
            func: Function to execute
            tasks: List of task arguments
            **kwargs: Additional arguments
            
        Returns:
            List of task results
        """
        if not self._is_initialized:
            self.initialize()
        
        self.monitor.start_monitoring()
        
        try:
            results = self._execute_impl(func, tasks, **kwargs)
        finally:
            self.monitor.stop_monitoring()
        
        return results
    
    def _execute_impl(
        self,
        func: Callable,
        tasks: List[Any],
        **kwargs
    ) -> List[TaskResult]:
        """Implementation-specific execution."""
        raise NotImplementedError
    
    def __enter__(self):
        self.initialize()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.shutdown()


class SequentialExecutor(BaseExecutor):
    """Sequential (single-threaded) executor."""
    
    def _execute_impl(
        self,
        func: Callable,
        tasks: List[Any],
        **kwargs
    ) -> List[TaskResult]:
        """Execute tasks sequentially."""
        results = []
        
        for i, task_args in enumerate(tasks):
            task_id = f"task_{i}"
            self.monitor.register_task(task_id)
            self.monitor.start_task(task_id, "main")
            
            try:
                # Execute task
                if isinstance(task_args, (list, tuple)):
                    result = func(*task_args, **kwargs)
                else:
                    result = func(task_args, **kwargs)
                
                self.monitor.complete_task(task_id, result)
                
                results.append(TaskResult(
                    task_id=task_id,
                    status=TaskStatus.COMPLETED,
                    result=result
                ))
                
            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}"
                self.monitor.complete_task(task_id, error=error_msg)
                
                results.append(TaskResult(
                    task_id=task_id,
                    status=TaskStatus.FAILED,
                    error=error_msg
                ))
                
                self.logger.error(f"Task {task_id} failed: {error_msg}")
        
        return results


class ThreadPoolExecutorWrapper(BaseExecutor):
    """Thread pool executor wrapper."""
    
    def __init__(self, config: ExecutionConfig):
        super().__init__(config)
        self.executor: Optional[ThreadPoolExecutor] = None
    
    def _initialize_impl(self) -> None:
        """Initialize thread pool."""
        max_workers = self.config.max_workers or min(32, (os.cpu_count() or 1) + 4)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
    
    def _shutdown_impl(self) -> None:
        """Shutdown thread pool."""
        if self.executor:
            self.executor.shutdown(wait=True)
    
    def _execute_impl(
        self,
        func: Callable,
        tasks: List[Any],
        **kwargs
    ) -> List[TaskResult]:
        """Execute tasks using thread pool."""
        if not self.executor:
            raise RuntimeError("Executor not initialized")
        
        # Submit all tasks
        future_to_task = {}
        for i, task_args in enumerate(tasks):
            task_id = f"task_{i}"
            self.monitor.register_task(task_id)
            
            # Create wrapper function
            def task_wrapper(args, task_id=task_id):
                self.monitor.start_task(task_id, threading.current_thread().name)
                try:
                    if isinstance(args, (list, tuple)):
                        result = func(*args, **kwargs)
                    else:
                        result = func(args, **kwargs)
                    return result
                except Exception as e:
                    raise e
            
            future = self.executor.submit(task_wrapper, task_args)
            future_to_task[future] = task_id
        
        # Collect results
        results = []
        for future in as_completed(future_to_task, timeout=self.config.timeout):
            task_id = future_to_task[future]
            
            try:
                result = future.result()
                self.monitor.complete_task(task_id, result)
                
                results.append(TaskResult(
                    task_id=task_id,
                    status=TaskStatus.COMPLETED,
                    result=result
                ))
                
            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}"
                self.monitor.complete_task(task_id, error=error_msg)
                
                results.append(TaskResult(
                    task_id=task_id,
                    status=TaskStatus.FAILED,
                    error=error_msg
                ))
        
        return results


class ProcessPoolExecutorWrapper(BaseExecutor):
    """Process pool executor wrapper."""
    
    def __init__(self, config: ExecutionConfig):
        super().__init__(config)
        self.executor: Optional[ProcessPoolExecutor] = None
    
    def _initialize_impl(self) -> None:
        """Initialize process pool."""
        max_workers = self.config.max_workers or os.cpu_count()
        self.executor = ProcessPoolExecutor(max_workers=max_workers)
    
    def _shutdown_impl(self) -> None:
        """Shutdown process pool."""
        if self.executor:
            self.executor.shutdown(wait=True)
    
    def _execute_impl(
        self,
        func: Callable,
        tasks: List[Any],
        **kwargs
    ) -> List[TaskResult]:
        """Execute tasks using process pool."""
        if not self.executor:
            raise RuntimeError("Executor not initialized")
        
        # Submit all tasks
        future_to_task = {}
        for i, task_args in enumerate(tasks):
            task_id = f"task_{i}"
            self.monitor.register_task(task_id)
            
            # For process pool, we need to use a wrapper that handles monitoring
            future = self.executor.submit(_process_task_wrapper, func, task_args, kwargs)
            future_to_task[future] = task_id
        
        # Collect results
        results = []
        for future in as_completed(future_to_task, timeout=self.config.timeout):
            task_id = future_to_task[future]
            
            try:
                result = future.result()
                self.monitor.complete_task(task_id, result)
                
                results.append(TaskResult(
                    task_id=task_id,
                    status=TaskStatus.COMPLETED,
                    result=result
                ))
                
            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}"
                self.monitor.complete_task(task_id, error=error_msg)
                
                results.append(TaskResult(
                    task_id=task_id,
                    status=TaskStatus.FAILED,
                    error=error_msg
                ))
        
        return results


def _process_task_wrapper(func: Callable, task_args: Any, kwargs: Dict) -> Any:
    """Wrapper function for process pool tasks."""
    try:
        if isinstance(task_args, (list, tuple)):
            return func(*task_args, **kwargs)
        else:
            return func(task_args, **kwargs)
    except Exception as e:
        # Re-raise with traceback for better debugging
        raise type(e)(f"{str(e)}\n{traceback.format_exc()}")


class RayExecutor(BaseExecutor):
    """Ray distributed executor."""
    
    def __init__(self, config: ExecutionConfig):
        super().__init__(config)
        
        if not RAY_AVAILABLE:
            raise ImportError("Ray is not available. Install with: pip install ray")
        
        self._ray_initialized = False
    
    def _initialize_impl(self) -> None:
        """Initialize Ray."""
        if not ray.is_initialized():
            init_kwargs = {
                'ignore_reinit_error': True
            }
            
            if self.config.ray_address:
                init_kwargs['address'] = self.config.ray_address  # type: ignore[assignment]
            
            if self.config.ray_runtime_env:
                init_kwargs['runtime_env'] = self.config.ray_runtime_env  # type: ignore[assignment]
            
            ray.init(**init_kwargs)
            self._ray_initialized = True
    
    def _shutdown_impl(self) -> None:
        """Shutdown Ray."""
        if self._ray_initialized and ray.is_initialized():
            ray.shutdown()
    
    def _execute_impl(
        self,
        func: Callable,
        tasks: List[Any],
        **kwargs
    ) -> List[TaskResult]:
        """Execute tasks using Ray."""
        # Convert function to Ray remote function
        remote_func = ray.remote(func)
        
        # Submit all tasks
        futures = []
        for i, task_args in enumerate(tasks):
            task_id = f"task_{i}"
            self.monitor.register_task(task_id)
            
            if isinstance(task_args, (list, tuple)):
                future = remote_func.remote(*task_args, **kwargs)
            else:
                future = remote_func.remote(task_args, **kwargs)
            
            futures.append((future, task_id))
        
        # Collect results
        results = []
        for future, task_id in futures:
            try:
                result = ray.get(future)
                self.monitor.complete_task(task_id, result)
                
                results.append(TaskResult(
                    task_id=task_id,
                    status=TaskStatus.COMPLETED,
                    result=result
                ))
                
            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}"
                self.monitor.complete_task(task_id, error=error_msg)
                
                results.append(TaskResult(
                    task_id=task_id,
                    status=TaskStatus.FAILED,
                    error=error_msg
                ))
        
        return results


class DaskExecutor(BaseExecutor):
    """Dask distributed executor."""
    
    def __init__(self, config: ExecutionConfig):
        super().__init__(config)
        
        if not DASK_AVAILABLE:
            raise ImportError("Dask is not available. Install with: pip install dask[distributed]")
        
        self.client: Optional[Client] = None
    
    def _initialize_impl(self) -> None:
        """Initialize Dask client."""
        client_kwargs = self.config.dask_client_kwargs or {}
        
        if self.config.dask_scheduler_address:
            client_kwargs['address'] = self.config.dask_scheduler_address
        
        self.client = Client(**client_kwargs)
    
    def _shutdown_impl(self) -> None:
        """Shutdown Dask client."""
        if self.client:
            self.client.close()
    
    def _execute_impl(
        self,
        func: Callable,
        tasks: List[Any],
        **kwargs
    ) -> List[TaskResult]:
        """Execute tasks using Dask."""
        if not self.client:
            raise RuntimeError("Dask client not initialized")
        
        # Create delayed tasks
        delayed_tasks = []
        for i, task_args in enumerate(tasks):
            task_id = f"task_{i}"
            self.monitor.register_task(task_id)
            
            if isinstance(task_args, (list, tuple)):
                delayed_task = delayed(func)(*task_args, **kwargs)
            else:
                delayed_task = delayed(func)(task_args, **kwargs)
            
            delayed_tasks.append((delayed_task, task_id))
        
        # Submit to cluster
        futures = []
        for delayed_task, task_id in delayed_tasks:
            future = self.client.compute(delayed_task)
            futures.append((future, task_id))
        
        # Collect results
        results = []
        for future, task_id in dask_as_completed(futures):
            try:
                result = future.result()
                self.monitor.complete_task(task_id, result)
                
                results.append(TaskResult(
                    task_id=task_id,
                    status=TaskStatus.COMPLETED,
                    result=result
                ))
                
            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}"
                self.monitor.complete_task(task_id, error=error_msg)
                
                results.append(TaskResult(
                    task_id=task_id,
                    status=TaskStatus.FAILED,
                    error=error_msg
                ))
        
        return results


class JoblibExecutor(BaseExecutor):
    """Joblib parallel executor."""
    
    def __init__(self, config: ExecutionConfig):
        super().__init__(config)
        
        if not JOBLIB_AVAILABLE:
            raise ImportError("Joblib is not available. Install with: pip install joblib")
    
    def _execute_impl(
        self,
        func: Callable,
        tasks: List[Any],
        **kwargs
    ) -> List[TaskResult]:
        """Execute tasks using Joblib."""
        # Register all tasks
        for i in range(len(tasks)):
            task_id = f"task_{i}"
            self.monitor.register_task(task_id)
        
        # Determine backend
        backend = 'threading' if self.config.mode == ExecutionMode.THREADING else 'multiprocessing'
        
        # Execute in parallel
        n_jobs = self.config.max_workers or -1
        
        try:
            with Parallel(n_jobs=n_jobs, backend=backend) as parallel:
                results_data = parallel(
                    joblib_delayed(_joblib_task_wrapper)(func, task_args, kwargs, i)
                    for i, task_args in enumerate(tasks)
                )
            
            # Convert to TaskResult objects
            results = []
            for i, (result, error) in enumerate(results_data):
                task_id = f"task_{i}"
                
                if error:
                    self.monitor.complete_task(task_id, error=error)
                    results.append(TaskResult(
                        task_id=task_id,
                        status=TaskStatus.FAILED,
                        error=error
                    ))
                else:
                    self.monitor.complete_task(task_id, result)
                    results.append(TaskResult(
                        task_id=task_id,
                        status=TaskStatus.COMPLETED,
                        result=result
                    ))
            
            return results
            
        except Exception as e:
            # Mark all tasks as failed
            error_msg = f"{type(e).__name__}: {str(e)}"
            results = []
            
            for i in range(len(tasks)):
                task_id = f"task_{i}"
                self.monitor.complete_task(task_id, error=error_msg)
                results.append(TaskResult(
                    task_id=task_id,
                    status=TaskStatus.FAILED,
                    error=error_msg
                ))
            
            return results


def _joblib_task_wrapper(func: Callable, task_args: Any, kwargs: Dict, task_index: int) -> Tuple[Any, Optional[str]]:
    """Wrapper function for Joblib tasks."""
    try:
        if isinstance(task_args, (list, tuple)):
            result = func(*task_args, **kwargs)
        else:
            result = func(task_args, **kwargs)
        return result, None
    except Exception as e:
        error_msg = f"{type(e).__name__}: {str(e)}"
        return None, error_msg


class ParallelExecutor:
    """Main parallel execution manager."""
    
    def __init__(self, config: Optional[ExecutionConfig] = None):
        """Initialize parallel executor.
        
        Args:
            config: Execution configuration
        """
        self.config = config or ExecutionConfig()
        self.logger = get_logger("parallel_executor")
        
        # Executor instance
        self._executor: Optional[BaseExecutor] = None
    
    def _get_executor(self) -> BaseExecutor:
        """Get executor instance based on configuration."""
        if self._executor is None:
            if self.config.mode == ExecutionMode.SEQUENTIAL:
                self._executor = SequentialExecutor(self.config)
            elif self.config.mode == ExecutionMode.THREADING:
                self._executor = ThreadPoolExecutorWrapper(self.config)
            elif self.config.mode == ExecutionMode.MULTIPROCESSING:
                self._executor = ProcessPoolExecutorWrapper(self.config)
            elif self.config.mode == ExecutionMode.RAY:
                self._executor = RayExecutor(self.config)
            elif self.config.mode == ExecutionMode.DASK:
                self._executor = DaskExecutor(self.config)
            elif self.config.mode == ExecutionMode.JOBLIB:
                self._executor = JoblibExecutor(self.config)
            else:
                raise ValueError(f"Unsupported execution mode: {self.config.mode}")
        
        return self._executor
    
    def execute(
        self,
        func: Callable,
        tasks: List[Any],
        **kwargs
    ) -> Tuple[List[TaskResult], ExecutionStats]:
        """Execute tasks in parallel.
        
        Args:
            func: Function to execute
            tasks: List of task arguments
            **kwargs: Additional function arguments
            
        Returns:
            Tuple of (results, execution_stats)
        """
        if not tasks:
            return [], ExecutionStats(
                total_tasks=0, completed_tasks=0, failed_tasks=0, cancelled_tasks=0,
                total_duration=0.0, avg_task_duration=0.0, min_task_duration=0.0, max_task_duration=0.0,
                total_memory_usage=0.0, peak_memory_usage=0.0, avg_memory_per_task=0.0,
                worker_count=0, cpu_utilization=0.0,
                start_time=datetime.now(), end_time=datetime.now()
            )
        
        self.logger.info(f"Executing {len(tasks)} tasks using {self.config.mode.value} mode")
        
        executor = self._get_executor()
        
        with executor:
            results = executor.execute(func, tasks, **kwargs)
            stats = executor.monitor.get_stats()
        
        self.logger.info(
            f"Execution completed: {stats.completed_tasks}/{stats.total_tasks} successful "
            f"({stats.success_rate:.1%}), {stats.total_duration:.2f}s total"
        )
        
        return results, stats
    
    def shutdown(self) -> None:
        """Shutdown executor."""
        if self._executor:
            self._executor.shutdown()
            self._executor = None


# Convenience functions
def execute_parallel(
    func: Callable,
    tasks: List[Any],
    mode: ExecutionMode = ExecutionMode.MULTIPROCESSING,
    max_workers: Optional[int] = None,
    **kwargs
) -> Tuple[List[TaskResult], ExecutionStats]:
    """Execute tasks in parallel using specified mode.
    
    Args:
        func: Function to execute
        tasks: List of task arguments
        mode: Execution mode
        max_workers: Maximum number of workers
        **kwargs: Additional function arguments
        
    Returns:
        Tuple of (results, execution_stats)
    """
    config = ExecutionConfig(mode=mode, max_workers=max_workers)
    executor = ParallelExecutor(config)
    
    try:
        return executor.execute(func, tasks, **kwargs)
    finally:
        executor.shutdown()


def get_optimal_worker_count(cpu_bound: bool = True) -> int:
    """Get optimal worker count based on system resources.
    
    Args:
        cpu_bound: Whether tasks are CPU-bound
        
    Returns:
        Optimal worker count
    """
    cpu_count = os.cpu_count() or 1
    
    if cpu_bound:
        # For CPU-bound tasks, use number of CPU cores
        return cpu_count
    else:
        # For I/O-bound tasks, use more workers
        return min(32, cpu_count * 4)


def benchmark_execution_modes(
    func: Callable,
    sample_tasks: List[Any],
    modes: Optional[List[ExecutionMode]] = None,
    **kwargs
) -> Dict[ExecutionMode, ExecutionStats]:
    """Benchmark different execution modes.
    
    Args:
        func: Function to execute
        sample_tasks: Sample tasks for benchmarking
        modes: Execution modes to benchmark
        **kwargs: Additional function arguments
        
    Returns:
        Dictionary of execution statistics by mode
    """
    if modes is None:
        modes = [ExecutionMode.SEQUENTIAL, ExecutionMode.THREADING, ExecutionMode.MULTIPROCESSING]
        
        # Add optional modes if available
        if RAY_AVAILABLE:
            modes.append(ExecutionMode.RAY)
        if JOBLIB_AVAILABLE:
            modes.append(ExecutionMode.JOBLIB)
    
    results = {}
    
    for mode in modes:
        try:
            _, stats = execute_parallel(func, sample_tasks, mode=mode, **kwargs)
            results[mode] = stats
        except Exception as e:
            print(f"Failed to benchmark {mode.value}: {e}")
    
    return results