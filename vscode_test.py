#!/usr/bin/env python3
"""
Test file to verify VSCode Python extension works
"""

import sys
import os

def main():
    print("=== VSCode Python Extension Test ===")
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    print(f"Current working directory: {os.getcwd()}")
    
    # Test if we can import our packages
    try:
        import numpy as np
        import pandas as pd
        print(f"✅ NumPy {np.__version__} imported successfully")
        print(f"✅ Pandas {pd.__version__} imported successfully")
    except ImportError as e:
        print(f"❌ Import error: {e}")
    
    print("=== Test completed ===")

if __name__ == "__main__":
    main()
