#!/usr/bin/env python3
"""
Test script to verify Python interpreter setup
"""

import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import yaml
from rich.console import Console

def main():
    console = Console()
    
    console.print("[bold green]Python Interpreter Test[/bold green]")
    console.print(f"Python version: {sys.version}")
    console.print(f"Python executable: {sys.executable}")
    
    console.print("\n[bold blue]Package Versions:[/bold blue]")
    console.print(f"NumPy: {np.__version__}")
    console.print(f"Pandas: {pd.__version__}")
    console.print(f"Matplotlib: {plt.matplotlib.__version__}")
    
    # Test basic functionality
    console.print("\n[bold yellow]Testing basic functionality...[/bold yellow]")
    
    # NumPy test
    arr = np.array([1, 2, 3, 4, 5])
    console.print(f"NumPy array sum: {arr.sum()}")
    
    # Pandas test
    df = pd.DataFrame({'A': [1, 2, 3], 'B': [4, 5, 6]})
    console.print(f"Pandas DataFrame shape: {df.shape}")
    
    # YAML test
    test_config = {'test': True, 'value': 42}
    yaml_str = yaml.dump(test_config)
    console.print(f"YAML serialization works: {yaml_str.strip()}")
    
    console.print("\n[bold green]✓ All tests passed! Python environment is working correctly.[/bold green]")

if __name__ == "__main__":
    main()
