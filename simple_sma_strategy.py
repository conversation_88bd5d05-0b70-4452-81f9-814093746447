"""Simple Moving Average Crossover Strategy.

Generated strategy: Simple SMA
"""

from typing import Dict, Any
import pandas as pd
import numpy as np
from ..strategies.base_strategy import BaseStrategy, OrderType, OrderSide


class SimpleSMAStrategy(BaseStrategy):
    """Simple Moving Average Crossover Strategy.
    
    Buys when short MA crosses above long MA,
    sells when short MA crosses below long MA.
    """
    
    def __init__(self, short_window: int = 10, long_window: int = 30):
        super().__init__()
        self.short_window = short_window
        self.long_window = long_window
        
        # Strategy state
        self.short_ma = None
        self.long_ma = None
        self.prev_short_ma = None
        self.prev_long_ma = None
    
    def on_data(self, data: pd.Series) -> None:
        """Process new market data."""
        # Update moving averages
        if len(self.data_buffer) >= self.long_window:
            prices = [d['close'] for d in self.data_buffer[-self.long_window:]]
            
            self.prev_short_ma = self.short_ma
            self.prev_long_ma = self.long_ma
            
            self.short_ma = np.mean(prices[-self.short_window:])
            self.long_ma = np.mean(prices)
            
            # Generate signals
            self._check_signals()
    
    def _check_signals(self) -> None:
        """Check for trading signals."""
        if self.prev_short_ma is None or self.prev_long_ma is None:
            return
        
        # Bullish crossover: short MA crosses above long MA
        if (self.prev_short_ma <= self.prev_long_ma and 
            self.short_ma > self.long_ma and 
            not self.position):
            
            self.buy(size=0.95)  # Use 95% of available capital
        
        # Bearish crossover: short MA crosses below long MA
        elif (self.prev_short_ma >= self.prev_long_ma and 
              self.short_ma < self.long_ma and 
              self.position):
            
            self.sell(size=1.0)  # Close entire position
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get strategy parameters."""
        return {
            'short_window': self.short_window,
            'long_window': self.long_window
        }
