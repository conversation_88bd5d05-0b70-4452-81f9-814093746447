#!/usr/bin/env python3
"""Simple backtest runner for the SMA crossover strategy."""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Import required modules
from src.strategies.base_strategy import BaseStrategy, OrderType, OrderSide
from src.engine.backtest_engine import BacktestEngine, BacktestConfig
from src.utils.logging import get_logger

# Set up logging
import logging
logging.basicConfig(level=logging.INFO)
logger = get_logger("simple_backtest")


class SimpleSMAStrategy(BaseStrategy):
    """Simple Moving Average Crossover Strategy.
    
    Buys when short MA crosses above long MA,
    sells when short MA crosses below long MA.
    """
    
    def __init__(self, short_window: int = 10, long_window: int = 30, strategy_id: str = "sma_crossover", symbols: list = None):
        if symbols is None:
            symbols = ["BTC/USD"]
        super().__init__(strategy_id=strategy_id, symbols=symbols)
        self.short_window = short_window
        self.long_window = long_window
        
        # Strategy state
        self.short_ma = None
        self.long_ma = None
        self.prev_short_ma = None
        self.prev_long_ma = None
        self.prices = []
    
    def on_data(self, data) -> None:
        """Process new market data."""
        # Store price data
        if hasattr(data, 'close'):
            price = data.close
        elif isinstance(data, dict):
            price = data['close']
        else:
            price = float(data)
            
        self.prices.append(price)
        
        # Keep only the data we need
        if len(self.prices) > self.long_window:
            self.prices = self.prices[-self.long_window:]
        
        # Calculate moving averages if we have enough data
        if len(self.prices) >= self.long_window:
            self.prev_short_ma = self.short_ma
            self.prev_long_ma = self.long_ma
            
            self.short_ma = np.mean(self.prices[-self.short_window:])
            self.long_ma = np.mean(self.prices)
            
            # Generate signals
            self._check_signals()
    
    def _check_signals(self) -> None:
        """Check for trading signals."""
        if self.prev_short_ma is None or self.prev_long_ma is None:
            return
        
        # Get current position
        position = self.get_position("BTC/USD")
        current_position_size = position.quantity if position else 0

        # Bullish crossover: short MA crosses above long MA
        if (self.prev_short_ma <= self.prev_long_ma and
            self.short_ma > self.long_ma and
            current_position_size <= 0):
            
            logger.info(f"BUY signal: Short MA ({self.short_ma:.2f}) crossed above Long MA ({self.long_ma:.2f})")
            # Calculate quantity based on available cash
            available_cash = self.get_available_cash() * 0.95  # Use 95% of available capital
            current_price = self.get_current_price("BTC/USD") or self.prices[-1]
            quantity = available_cash / current_price if current_price > 0 else 0

            if quantity > 0:
                from src.strategies.base_strategy import OrderSide, OrderType
                self.create_order("BTC/USD", OrderSide.BUY, OrderType.MARKET, quantity)
        
        # Bearish crossover: short MA crosses below long MA
        elif (self.prev_short_ma >= self.prev_long_ma and
              self.short_ma < self.long_ma and
              current_position_size > 0):
            
            logger.info(f"SELL signal: Short MA ({self.short_ma:.2f}) crossed below Long MA ({self.long_ma:.2f})")
            # Sell entire position
            if current_position_size > 0:
                from src.strategies.base_strategy import OrderSide, OrderType
                self.create_order("BTC/USD", OrderSide.SELL, OrderType.MARKET, current_position_size)
    
    def on_order_filled(self, order, trade) -> None:
        """Handle order fill event."""
        logger.info(f"Order filled: {order.side} {order.quantity} at {trade.price}")

    def get_parameters(self):
        """Get strategy parameters."""
        return {
            'short_window': self.short_window,
            'long_window': self.long_window
        }


def load_data(file_path: str) -> pd.DataFrame:
    """Load market data from CSV file."""
    logger.info(f"Loading data from {file_path}")
    
    data = pd.read_csv(file_path)
    
    # Convert datetime column
    if 'datetime' in data.columns:
        data['datetime'] = pd.to_datetime(data['datetime'])
        data.set_index('datetime', inplace=True)
    elif data.index.name != 'datetime':
        # Assume first column is datetime
        data.index = pd.to_datetime(data.index)
    
    logger.info(f"Loaded {len(data)} records from {data.index[0]} to {data.index[-1]}")
    return data


def run_simple_backtest():
    """Run a simple backtest with the SMA strategy."""
    
    # Load data
    data_file = "data/raw/coinbase_BTC-USD_1h_20240804_20250804.csv"
    data = load_data(data_file)
    
    # Create strategy
    strategy = SimpleSMAStrategy(short_window=10, long_window=30)
    
    # Create backtest configuration
    config = BacktestConfig(
        start_date=data.index[0],
        end_date=data.index[-1],
        initial_cash=10000.0,
        commission_rate=0.001,
        slippage=0.0001
    )
    
    logger.info("Starting backtest...")
    logger.info(f"Initial capital: ${config.initial_cash:,.2f}")
    logger.info(f"Commission rate: {config.commission_rate:.3%}")
    logger.info(f"Period: {config.start_date} to {config.end_date}")
    
    # Create and run backtest engine
    engine = BacktestEngine(config)
    
    # Simple backtest simulation
    portfolio_value = config.initial_cash
    cash = config.initial_cash
    position = 0
    trades = []
    equity_curve = []
    
    logger.info("Running backtest simulation...")
    
    for i, (timestamp, row) in enumerate(data.iterrows()):
        # Feed data to strategy
        strategy.on_data(row)
        
        # Check for orders (simplified)
        # In a real implementation, this would be handled by the engine
        
        # Update portfolio value
        if position > 0:
            portfolio_value = cash + (position * row['close'])
        else:
            portfolio_value = cash
            
        equity_curve.append((timestamp, portfolio_value))
        
        # Log progress every 1000 bars
        if i % 1000 == 0:
            logger.info(f"Processed {i}/{len(data)} bars, Portfolio: ${portfolio_value:,.2f}")
    
    # Calculate final results
    final_value = portfolio_value
    total_return = (final_value - config.initial_cash) / config.initial_cash
    
    logger.info("Backtest completed!")
    logger.info(f"Initial capital: ${config.initial_cash:,.2f}")
    logger.info(f"Final value: ${final_value:,.2f}")
    logger.info(f"Total return: {total_return:.2%}")
    logger.info(f"Total bars processed: {len(data)}")
    
    # Create simple equity curve plot data
    equity_df = pd.DataFrame(equity_curve, columns=['timestamp', 'portfolio_value'])
    equity_df.set_index('timestamp', inplace=True)
    
    # Save results
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    equity_df.to_csv(results_dir / "simple_backtest_equity_curve.csv")
    logger.info(f"Results saved to {results_dir / 'simple_backtest_equity_curve.csv'}")
    
    return {
        'initial_capital': config.initial_cash,
        'final_value': final_value,
        'total_return': total_return,
        'total_bars': len(data),
        'equity_curve': equity_df
    }


if __name__ == "__main__":
    try:
        results = run_simple_backtest()
        print("\n" + "="*50)
        print("BACKTEST RESULTS")
        print("="*50)
        print(f"Initial Capital: ${results['initial_capital']:,.2f}")
        print(f"Final Value: ${results['final_value']:,.2f}")
        print(f"Total Return: {results['total_return']:.2%}")
        print(f"Bars Processed: {results['total_bars']:,}")
        print("="*50)
        
    except Exception as e:
        logger.error(f"Backtest failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
