@echo off
REM Python Development Helper Script
REM Usage: python_dev.bat [command] [file]

set PYTHON_PATH="C:\Users\<USER>\anaconda3\envs\backtester4_py311\python.exe"

if "%1"=="run" (
    echo Running Python script: %2
    %PYTHON_PATH% %2
    goto :end
)

if "%1"=="test" (
    echo Running tests...
    %PYTHON_PATH% -m pytest %2
    goto :end
)

if "%1"=="install" (
    echo Installing package: %2
    %PYTHON_PATH% -m pip install %2
    goto :end
)

if "%1"=="shell" (
    echo Starting Python interactive shell...
    %PYTHON_PATH%
    goto :end
)

if "%1"=="check" (
    echo Checking Python environment...
    %PYTHON_PATH% --version
    %PYTHON_PATH% -c "import sys; print('Python executable:', sys.executable)"
    goto :end
)

echo Usage:
echo   python_dev.bat run [script.py]     - Run a Python script
echo   python_dev.bat test [test_file]    - Run tests
echo   python_dev.bat install [package]   - Install a package
echo   python_dev.bat shell               - Start Python shell
echo   python_dev.bat check               - Check environment

:end
