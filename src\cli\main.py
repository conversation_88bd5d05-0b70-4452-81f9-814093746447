"""Command-line interface for the cryptocurrency backtesting system.

This module provides a comprehensive CLI for running backtests, optimizations,
and managing the backtesting system.
"""

import os
import sys
import json
import yaml
import click
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.panel import Panel
from rich.text import Text
from rich import print as rprint
from rich.prompt import Prompt, Confirm
from rich.syntax import Syntax

# Import backtesting modules
from ..data.fetcher import CryptoDataFetcher
from ..data.preprocessor import DataPreprocessor
from ..data.storage import DataStorage
from ..strategies.base_strategy import BaseStrategy
from ..engine.backtest_engine import BacktestEngine, BacktestConfig
from ..optimization.parallel_optimizer import (
    ParallelOptimizer, OptimizationConfig, OptimizationMethod, ExecutionMode
)
from ..parallel.executor import execute_parallel, benchmark_execution_modes
from ..analysis.performance import PerformanceAnalyzer, ReportGenerator
from ..utils.config import ConfigManager, get_config
from ..utils.logging import get_logger
from ..utils import setup_logging
from ..monitoring.metrics import get_metrics_manager

# Initialize console and logger
console = Console()
logger = get_logger("cli")


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), help='Configuration file path')
@click.option('--log-level', type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR']), default='INFO')
@click.option('--quiet', '-q', is_flag=True, help='Suppress output')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.pass_context
def cli(ctx, config, log_level, quiet, verbose):
    """Cryptocurrency Backtesting System CLI.
    
    A comprehensive tool for backtesting cryptocurrency trading strategies
    with parallel optimization capabilities.
    """
    # Ensure context object exists
    ctx.ensure_object(dict)
    
    # Set up logging
    if verbose:
        log_level = 'DEBUG'
    elif quiet:
        log_level = 'ERROR'
    
    setup_logging(level=log_level)
    
    # Load configuration
    if config:
        ctx.obj['config_path'] = config
    
    # Store CLI options
    ctx.obj['quiet'] = quiet
    ctx.obj['verbose'] = verbose
    
    if not quiet:
        console.print("[bold blue]Cryptocurrency Backtesting System[/bold blue]")
        console.print("[dim]Advanced parallel backtesting and optimization[/dim]\n")


@cli.group()
def data():
    """Data management commands."""
    pass


@data.command()
@click.option('--exchange', '-e', required=True, help='Exchange name (e.g., binance, coinbase)')
@click.option('--symbol', '-s', required=True, help='Trading symbol (e.g., BTC/USDT)')
@click.option('--timeframe', '-t', default='1h', help='Timeframe (e.g., 1m, 5m, 1h, 1d)')
@click.option('--start', type=click.DateTime(), help='Start date (YYYY-MM-DD)')
@click.option('--end', type=click.DateTime(), help='End date (YYYY-MM-DD)')
@click.option('--days', type=int, help='Number of days to fetch (from end date backwards)')
@click.option('--output', '-o', type=click.Path(), help='Output file path')
@click.option('--format', 'output_format', type=click.Choice(['csv', 'hdf5', 'parquet']), default='hdf5')
@click.pass_context
def fetch(ctx, exchange, symbol, timeframe, start, end, days, output, output_format):
    """Fetch historical cryptocurrency data."""
    try:
        # Initialize data fetcher
        fetcher = CryptoDataFetcher()
        
        # Determine date range
        if days:
            end_date = end or datetime.now()
            start_date = end_date - timedelta(days=days)
        else:
            start_date = start
            end_date = end
        
        if not start_date:
            start_date = datetime.now() - timedelta(days=30)  # Default to 30 days
        if not end_date:
            end_date = datetime.now()
        
        console.print(f"[blue]Fetching {symbol} data from {exchange}[/blue]")
        console.print(f"Timeframe: {timeframe}")
        console.print(f"Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TimeElapsedColumn(),
            console=console
        ) as progress:
            task = progress.add_task("Fetching data...", total=None)
            
            # Fetch data
            data = fetcher.fetch_ohlcv_range(
                exchange=exchange,
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date
            )
            
            progress.update(task, description=f"Fetched {len(data)} records")
        
        if data.empty:
            console.print("[red]No data fetched[/red]")
            return
        
        # Save data
        if output:
            output_path = Path(output)
        else:
            # Generate default filename
            filename = f"{exchange}_{symbol.replace('/', '_')}_{timeframe}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}"
            output_path = Path(f"{filename}.{output_format}")
        
        # Ensure output directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        if output_format == 'csv':
            data.to_csv(output_path)
        elif output_format == 'hdf5':
            data.to_hdf(output_path, key='data', mode='w')
        elif output_format == 'parquet':
            data.to_parquet(output_path)
        
        console.print(f"[green]Data saved to {output_path}[/green]")
        
        # Display summary
        summary_table = Table(title="Data Summary")
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Value", style="white")
        
        summary_table.add_row("Records", str(len(data)))
        summary_table.add_row("Start Date", data.index[0].strftime('%Y-%m-%d %H:%M:%S'))
        summary_table.add_row("End Date", data.index[-1].strftime('%Y-%m-%d %H:%M:%S'))
        summary_table.add_row("Price Range", f"${data['low'].min():.2f} - ${data['high'].max():.2f}")
        summary_table.add_row("Total Volume", f"{data['volume'].sum():,.0f}")
        
        console.print(summary_table)
        
    except Exception as e:
        console.print(f"[red]Error fetching data: {e}[/red]")
        if ctx.obj.get('verbose'):
            console.print_exception()
        sys.exit(1)


@data.command()
@click.argument('input_file', type=click.Path(exists=True))
@click.option('--output', '-o', type=click.Path(), help='Output file path')
@click.option('--clean', is_flag=True, help='Clean data (remove outliers, fill gaps)')
@click.option('--features', is_flag=True, help='Add technical indicators')
@click.option('--config-file', type=click.Path(exists=True), help='Preprocessing configuration file')
@click.pass_context
def preprocess(ctx, input_file, output, clean, features, config_file):
    """Preprocess cryptocurrency data."""
    try:
        # Load data
        input_path = Path(input_file)
        
        if input_path.suffix == '.csv':
            data = pd.read_csv(input_path, index_col=0, parse_dates=True)
        elif input_path.suffix == '.h5':
            data = pd.read_hdf(input_path)
        elif input_path.suffix == '.parquet':
            data = pd.read_parquet(input_path)
        else:
            raise ValueError(f"Unsupported file format: {input_path.suffix}")
        
        console.print(f"[blue]Preprocessing {len(data)} records[/blue]")
        
        # Initialize preprocessor
        preprocessor = DataPreprocessor()
        
        # Load configuration if provided
        if config_file:
            with open(config_file, 'r') as f:
                config_data = yaml.safe_load(f)
                # Apply configuration to preprocessor
                # This would need to be implemented based on your config structure
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            # Data quality assessment
            task = progress.add_task("Assessing data quality...", total=None)
            quality_report = preprocessor.assess_data_quality(data)
            
            # Clean data if requested
            if clean:
                progress.update(task, description="Cleaning data...")
                data = preprocessor.clean_data(data)
            
            # Add features if requested
            if features:
                progress.update(task, description="Adding technical features...")
                data = preprocessor.add_technical_features(data)
                data = preprocessor.add_time_features(data)
                data = preprocessor.add_volatility_features(data)
        
        # Save processed data
        if output:
            output_path = Path(output)
        else:
            output_path = input_path.with_suffix('.processed' + input_path.suffix)
        
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        if output_path.suffix == '.csv':
            data.to_csv(output_path)
        elif output_path.suffix == '.h5':
            data.to_hdf(output_path, key='data', mode='w')
        elif output_path.suffix == '.parquet':
            data.to_parquet(output_path)
        
        console.print(f"[green]Processed data saved to {output_path}[/green]")
        
        # Display quality report
        if not ctx.obj.get('quiet'):
            quality_table = Table(title="Data Quality Report")
            quality_table.add_column("Metric", style="cyan")
            quality_table.add_column("Value", style="white")
            
            quality_table.add_row("Missing Values", str(quality_report.missing_values))
            quality_table.add_row("Duplicate Records", str(quality_report.duplicate_records))
            quality_table.add_row("Outliers Detected", str(quality_report.outliers_detected))
            quality_table.add_row("Data Gaps", str(quality_report.data_gaps))
            quality_table.add_row("Quality Score", f"{quality_report.quality_score:.2%}")
            
            console.print(quality_table)
        
    except Exception as e:
        console.print(f"[red]Error preprocessing data: {e}[/red]")
        if ctx.obj.get('verbose'):
            console.print_exception()
        sys.exit(1)


@cli.group()
def strategy():
    """Strategy management commands."""
    pass


@strategy.command()
@click.option('--name', '-n', required=True, help='Strategy name')
@click.option('--template', type=click.Choice(['sma_crossover', 'rsi_mean_reversion', 'bollinger_bands']), 
              default='sma_crossover', help='Strategy template')
@click.option('--output', '-o', type=click.Path(), help='Output file path')
def create(name, template, output):
    """Create a new trading strategy from template."""
    try:
        # Strategy templates
        templates = {
            'sma_crossover': _generate_sma_crossover_strategy,
            'rsi_mean_reversion': _generate_rsi_strategy,
            'bollinger_bands': _generate_bollinger_strategy
        }
        
        if template not in templates:
            console.print(f"[red]Unknown template: {template}[/red]")
            return
        
        # Generate strategy code
        strategy_code = templates[template](name)
        
        # Save strategy
        if output:
            output_path = Path(output)
        else:
            output_path = Path(f"{name.lower().replace(' ', '_')}_strategy.py")
        
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w') as f:
            f.write(strategy_code)
        
        console.print(f"[green]Strategy created: {output_path}[/green]")
        
        # Display strategy code
        syntax = Syntax(strategy_code, "python", theme="monokai", line_numbers=True)
        console.print(Panel(syntax, title=f"Strategy: {name}", expand=False))
        
    except Exception as e:
        console.print(f"[red]Error creating strategy: {e}[/red]")
        sys.exit(1)


@cli.group()
def backtest():
    """Backtesting commands."""
    pass


@backtest.command()
@click.option('--strategy', '-s', required=True, type=click.Path(exists=True), help='Strategy file path')
@click.option('--data', '-d', required=True, type=click.Path(exists=True), help='Data file path')
@click.option('--start-date', type=click.DateTime(), help='Backtest start date')
@click.option('--end-date', type=click.DateTime(), help='Backtest end date')
@click.option('--initial-capital', type=float, default=10000.0, help='Initial capital')
@click.option('--commission', type=float, default=0.001, help='Commission rate')
@click.option('--output', '-o', type=click.Path(), help='Output directory for results')
@click.option('--plot', is_flag=True, help='Generate performance plots')
@click.pass_context
def run(ctx, strategy, data, start_date, end_date, initial_capital, commission, output, plot):
    """Run a single backtest."""
    try:
        console.print("[blue]Running backtest...[/blue]")
        
        # Load data
        data_path = Path(data)
        if data_path.suffix == '.csv':
            market_data = pd.read_csv(data_path, index_col=0, parse_dates=True)
        elif data_path.suffix == '.h5':
            market_data = pd.read_hdf(data_path)
        elif data_path.suffix == '.parquet':
            market_data = pd.read_parquet(data_path)
        else:
            raise ValueError(f"Unsupported data format: {data_path.suffix}")
        
        # Filter data by date range
        if start_date:
            market_data = market_data[market_data.index >= start_date]
        if end_date:
            market_data = market_data[market_data.index <= end_date]
        
        console.print(f"Data period: {market_data.index[0]} to {market_data.index[-1]}")
        console.print(f"Records: {len(market_data)}")
        
        # Load strategy
        strategy_path = Path(strategy)
        # This would need dynamic strategy loading implementation
        # For now, we'll create a simple example
        
        # Create backtest configuration
        config = BacktestConfig(
            initial_capital=initial_capital,
            commission=commission,
            start_date=start_date,
            end_date=end_date
        )
        
        # Run backtest
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TimeElapsedColumn(),
            console=console
        ) as progress:
            task = progress.add_task("Running backtest...", total=len(market_data))
            
            engine = BacktestEngine(config)
            # This would need actual strategy instance
            # result = engine.run_backtest(strategy_instance, market_data)
            
            progress.update(task, completed=len(market_data))
        
        # For demonstration, create mock results
        console.print("[green]Backtest completed![/green]")
        
        # Display results summary
        results_table = Table(title="Backtest Results")
        results_table.add_column("Metric", style="cyan")
        results_table.add_column("Value", style="white")
        
        # Mock results - replace with actual results
        results_table.add_row("Total Return", "15.23%")
        results_table.add_row("Sharpe Ratio", "1.45")
        results_table.add_row("Max Drawdown", "-8.76%")
        results_table.add_row("Win Rate", "62.5%")
        results_table.add_row("Total Trades", "156")
        
        console.print(results_table)
        
        # Save results if output specified
        if output:
            output_path = Path(output)
            output_path.mkdir(parents=True, exist_ok=True)
            console.print(f"[green]Results saved to {output_path}[/green]")
        
    except Exception as e:
        console.print(f"[red]Error running backtest: {e}[/red]")
        if ctx.obj.get('verbose'):
            console.print_exception()
        sys.exit(1)


@cli.group()
def optimize():
    """Optimization commands."""
    pass


@optimize.command()
@click.option('--strategy', '-s', required=True, type=click.Path(exists=True), help='Strategy file path')
@click.option('--data', '-d', required=True, type=click.Path(exists=True), help='Data file path')
@click.option('--params', '-p', required=True, type=click.Path(exists=True), help='Parameter space file (JSON/YAML)')
@click.option('--method', type=click.Choice(['grid', 'random', 'bayesian', 'optuna']), 
              default='grid', help='Optimization method')
@click.option('--mode', type=click.Choice(['sequential', 'threading', 'multiprocessing', 'ray']), 
              default='multiprocessing', help='Execution mode')
@click.option('--workers', type=int, help='Number of parallel workers')
@click.option('--iterations', type=int, default=100, help='Number of optimization iterations')
@click.option('--output', '-o', type=click.Path(), help='Output directory for results')
@click.pass_context
def run_optimization(ctx, strategy, data, params, method, mode, workers, iterations, output):
    """Run parameter optimization."""
    try:
        console.print("[blue]Starting parameter optimization...[/blue]")
        
        # Load parameter space
        params_path = Path(params)
        if params_path.suffix == '.json':
            with open(params_path, 'r') as f:
                param_space = json.load(f)
        elif params_path.suffix in ['.yml', '.yaml']:
            with open(params_path, 'r') as f:
                param_space = yaml.safe_load(f)
        else:
            raise ValueError(f"Unsupported parameter file format: {params_path.suffix}")
        
        # Load data
        data_path = Path(data)
        if data_path.suffix == '.csv':
            market_data = pd.read_csv(data_path, index_col=0, parse_dates=True)
        elif data_path.suffix == '.h5':
            market_data = pd.read_hdf(data_path)
        elif data_path.suffix == '.parquet':
            market_data = pd.read_parquet(data_path)
        
        console.print(f"Parameter space: {len(param_space)} parameters")
        console.print(f"Data period: {market_data.index[0]} to {market_data.index[-1]}")
        console.print(f"Optimization method: {method}")
        console.print(f"Execution mode: {mode}")
        
        # Create optimization configuration
        opt_config = OptimizationConfig(
            method=OptimizationMethod(method.upper()),
            execution_mode=ExecutionMode(mode.upper()),
            max_workers=workers,
            max_iterations=iterations
        )
        
        # Run optimization
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TimeElapsedColumn(),
            console=console
        ) as progress:
            task = progress.add_task("Optimizing parameters...", total=iterations)
            
            optimizer = ParallelOptimizer(opt_config)
            
            # This would need actual implementation
            # results = optimizer.optimize(strategy, market_data, param_space)
            
            progress.update(task, completed=iterations)
        
        console.print("[green]Optimization completed![/green]")
        
        # Display results summary
        results_table = Table(title="Optimization Results")
        results_table.add_column("Metric", style="cyan")
        results_table.add_column("Value", style="white")
        
        # Mock results - replace with actual results
        results_table.add_row("Best Return", "23.45%")
        results_table.add_row("Best Sharpe", "1.87")
        results_table.add_row("Iterations", str(iterations))
        results_table.add_row("Success Rate", "94.2%")
        
        console.print(results_table)
        
        # Save results if output specified
        if output:
            output_path = Path(output)
            output_path.mkdir(parents=True, exist_ok=True)
            console.print(f"[green]Results saved to {output_path}[/green]")
        
    except Exception as e:
        console.print(f"[red]Error running optimization: {e}[/red]")
        if ctx.obj.get('verbose'):
            console.print_exception()
        sys.exit(1)


@cli.group()
def system():
    """System management commands."""
    pass


@system.command()
def status():
    """Show system status and configuration."""
    try:
        # System information
        import psutil
        
        status_table = Table(title="System Status")
        status_table.add_column("Component", style="cyan")
        status_table.add_column("Status", style="white")
        status_table.add_column("Details", style="dim")
        
        # CPU
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        status_table.add_row("CPU", f"{cpu_percent:.1f}%", f"{cpu_count} cores")
        
        # Memory
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        status_table.add_row("Memory", f"{memory.percent:.1f}%", f"{memory_gb:.1f} GB total")
        
        # Disk
        try:
            import os
            current_drive = os.getcwd()[:3]  # Get drive letter like 'C:\\'
            disk = psutil.disk_usage(current_drive)
            disk_gb = disk.total / (1024**3)
            disk_percent = (disk.used / disk.total) * 100
            status_table.add_row("Disk", f"{disk_percent:.1f}%", f"{disk_gb:.1f} GB total")
        except Exception:
            # Fallback to current directory
            try:
                disk = psutil.disk_usage('.')
                disk_gb = disk.total / (1024**3)
                disk_percent = (disk.used / disk.total) * 100
                status_table.add_row("Disk", f"{disk_percent:.1f}%", f"{disk_gb:.1f} GB total")
            except Exception:
                status_table.add_row("Disk", "N/A", "Unable to get disk info")
        
        # Dependencies
        try:
            import ray  # type: ignore[import-not-found]
            ray_status = "Available"
        except ImportError:
            ray_status = "Not installed"
        status_table.add_row("Ray", ray_status, "Distributed computing")
        
        try:
            import dask
            dask_status = "Available"
        except ImportError:
            dask_status = "Not installed"
        status_table.add_row("Dask", dask_status, "Parallel computing")
        
        console.print(status_table)
        
        # Configuration
        config = get_config()
        config_table = Table(title="Configuration")
        config_table.add_column("Setting", style="cyan")
        config_table.add_column("Value", style="white")
        
        # Add configuration details here
        config_table.add_row("Log Level", "INFO")
        config_table.add_row("Data Directory", "./data")
        config_table.add_row("Results Directory", "./results")
        
        console.print(config_table)
        
    except Exception as e:
        console.print(f"[red]Error getting system status: {e}[/red]")
        sys.exit(1)


@system.command()
@click.option('--sample-size', type=int, default=1000, help='Sample size for benchmarking')
def benchmark(sample_size):
    """Benchmark parallel execution modes."""
    try:
        console.print("[blue]Benchmarking parallel execution modes...[/blue]")
        
        # Create sample computation task
        def sample_task(x):
            # Simulate CPU-intensive work
            return sum(i**2 for i in range(x))
        
        # Generate sample tasks
        tasks = [sample_size // 10] * 10
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Running benchmarks...", total=None)
            
            # Benchmark different modes
            results = benchmark_execution_modes(sample_task, tasks)
        
        # Display results
        benchmark_table = Table(title="Execution Mode Benchmark")
        benchmark_table.add_column("Mode", style="cyan")
        benchmark_table.add_column("Duration (s)", style="white")
        benchmark_table.add_column("Throughput (tasks/s)", style="white")
        benchmark_table.add_column("Success Rate", style="white")
        
        for mode, stats in results.items():
            benchmark_table.add_row(
                mode.value,
                f"{stats.total_duration:.2f}",
                f"{stats.throughput:.1f}",
                f"{stats.success_rate:.1%}"
            )
        
        console.print(benchmark_table)
        
        # Recommend best mode
        best_mode = min(results.items(), key=lambda x: x[1].total_duration)
        console.print(f"\n[green]Recommended mode: {best_mode[0].value}[/green]")
        
    except Exception as e:
        console.print(f"[red]Error running benchmark: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.option('--port', type=int, default=8080, help='Dashboard port')
@click.option('--host', default='localhost', help='Dashboard host')
def dashboard(port, host):
    """Launch monitoring dashboard."""
    try:
        console.print(f"[blue]Starting dashboard on {host}:{port}[/blue]")
        console.print("[dim]Press Ctrl+C to stop[/dim]")
        
        # This would launch a web dashboard
        # For now, just show a message
        console.print("[yellow]Dashboard feature coming soon![/yellow]")
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Dashboard stopped[/yellow]")
    except Exception as e:
        console.print(f"[red]Error starting dashboard: {e}[/red]")
        sys.exit(1)


# Strategy template generators
def _generate_sma_crossover_strategy(name: str) -> str:
    """Generate SMA crossover strategy template."""
    return f'''"""Simple Moving Average Crossover Strategy.

Generated strategy: {name}
"""

from typing import Dict, Any
import pandas as pd
import numpy as np
from ..strategies.base_strategy import BaseStrategy, OrderType, OrderSide


class {name.replace(' ', '')}Strategy(BaseStrategy):
    """Simple Moving Average Crossover Strategy.
    
    Buys when short MA crosses above long MA,
    sells when short MA crosses below long MA.
    """
    
    def __init__(self, short_window: int = 10, long_window: int = 30):
        super().__init__()
        self.short_window = short_window
        self.long_window = long_window
        
        # Strategy state
        self.short_ma = None
        self.long_ma = None
        self.prev_short_ma = None
        self.prev_long_ma = None
    
    def on_data(self, data: pd.Series) -> None:
        """Process new market data."""
        # Update moving averages
        if len(self.data_buffer) >= self.long_window:
            prices = [d['close'] for d in self.data_buffer[-self.long_window:]]
            
            self.prev_short_ma = self.short_ma
            self.prev_long_ma = self.long_ma
            
            self.short_ma = np.mean(prices[-self.short_window:])
            self.long_ma = np.mean(prices)
            
            # Generate signals
            self._check_signals()
    
    def _check_signals(self) -> None:
        """Check for trading signals."""
        if self.prev_short_ma is None or self.prev_long_ma is None:
            return
        
        # Bullish crossover: short MA crosses above long MA
        if (self.prev_short_ma <= self.prev_long_ma and 
            self.short_ma > self.long_ma and 
            not self.position):
            
            self.buy(size=0.95)  # Use 95% of available capital
        
        # Bearish crossover: short MA crosses below long MA
        elif (self.prev_short_ma >= self.prev_long_ma and 
              self.short_ma < self.long_ma and 
              self.position):
            
            self.sell(size=1.0)  # Close entire position
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get strategy parameters."""
        return {{
            'short_window': self.short_window,
            'long_window': self.long_window
        }}
'''


def _generate_rsi_strategy(name: str) -> str:
    """Generate RSI mean reversion strategy template."""
    return f'''"""RSI Mean Reversion Strategy.

Generated strategy: {name}
"""

from typing import Dict, Any
import pandas as pd
import numpy as np
from ..strategies.base_strategy import BaseStrategy, OrderType, OrderSide


class {name.replace(' ', '')}Strategy(BaseStrategy):
    """RSI Mean Reversion Strategy.
    
    Buys when RSI is oversold (< oversold_threshold),
    sells when RSI is overbought (> overbought_threshold).
    """
    
    def __init__(self, rsi_period: int = 14, oversold_threshold: float = 30, 
                 overbought_threshold: float = 70):
        super().__init__()
        self.rsi_period = rsi_period
        self.oversold_threshold = oversold_threshold
        self.overbought_threshold = overbought_threshold
        
        # Strategy state
        self.rsi = None
    
    def on_data(self, data: pd.Series) -> None:
        """Process new market data."""
        if len(self.data_buffer) >= self.rsi_period + 1:
            # Calculate RSI
            prices = [d['close'] for d in self.data_buffer[-(self.rsi_period + 1):]]
            self.rsi = self._calculate_rsi(prices)
            
            # Generate signals
            self._check_signals()
    
    def _calculate_rsi(self, prices: list) -> float:
        """Calculate RSI."""
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains)
        avg_loss = np.mean(losses)
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def _check_signals(self) -> None:
        """Check for trading signals."""
        if self.rsi is None:
            return
        
        # Buy signal: RSI oversold
        if self.rsi < self.oversold_threshold and not self.position:
            self.buy(size=0.95)
        
        # Sell signal: RSI overbought
        elif self.rsi > self.overbought_threshold and self.position:
            self.sell(size=1.0)
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get strategy parameters."""
        return {{
            'rsi_period': self.rsi_period,
            'oversold_threshold': self.oversold_threshold,
            'overbought_threshold': self.overbought_threshold
        }}
'''


def _generate_bollinger_strategy(name: str) -> str:
    """Generate Bollinger Bands strategy template."""
    return f'''"""Bollinger Bands Strategy.

Generated strategy: {name}
"""

from typing import Dict, Any
import pandas as pd
import numpy as np
from ..strategies.base_strategy import BaseStrategy, OrderType, OrderSide


class {name.replace(' ', '')}Strategy(BaseStrategy):
    """Bollinger Bands Strategy.
    
    Buys when price touches lower band,
    sells when price touches upper band.
    """
    
    def __init__(self, period: int = 20, std_dev: float = 2.0):
        super().__init__()
        self.period = period
        self.std_dev = std_dev
        
        # Strategy state
        self.upper_band = None
        self.lower_band = None
        self.middle_band = None
    
    def on_data(self, data: pd.Series) -> None:
        """Process new market data."""
        if len(self.data_buffer) >= self.period:
            # Calculate Bollinger Bands
            prices = [d['close'] for d in self.data_buffer[-self.period:]]
            
            self.middle_band = np.mean(prices)
            std = np.std(prices)
            
            self.upper_band = self.middle_band + (self.std_dev * std)
            self.lower_band = self.middle_band - (self.std_dev * std)
            
            # Generate signals
            self._check_signals()
    
    def _check_signals(self) -> None:
        """Check for trading signals."""
        if self.upper_band is None or self.lower_band is None:
            return
        
        current_price = self.current_data['close']
        
        # Buy signal: price touches lower band
        if current_price <= self.lower_band and not self.position:
            self.buy(size=0.95)
        
        # Sell signal: price touches upper band
        elif current_price >= self.upper_band and self.position:
            self.sell(size=1.0)
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get strategy parameters."""
        return {{
            'period': self.period,
            'std_dev': self.std_dev
        }}
'''


if __name__ == '__main__':
    cli()